// Location models for handling country_state_city package data
class LocationItem {
  final String id;
  final String name;

  LocationItem({
    required this.id,
    required this.name,
  });

  factory LocationItem.fromCountryStateCity(dynamic item) {
    return LocationItem(
      id: item.id?.toString() ?? '',
      name: item.name?.toString() ?? '',
    );
  }

  @override
  String toString() => name;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocationItem &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

class PatientRegistrationResponse {
  final List<PatientRegistration> data;
  final Pagination pagination;

  PatientRegistrationResponse({
    required this.data,
    required this.pagination,
  });

  factory PatientRegistrationResponse.fromJson(Map<String, dynamic> json) {
    return PatientRegistrationResponse(
      data: (json['data'] as List)
          .map((item) => PatientRegistration.fromJson(item))
          .toList(),
      pagination: Pagination.fromJson(json['pagination']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.map((item) => item.toJson()).toList(),
      'pagination': pagination.toJson(),
    };
  }
}

class PatientRegistration {
  final String? id;
  final String? firstName;
  final String? lastName;
  final String? name; // Combined first_name + last_name from API
  final String? email;
  final String? mobile;
  final String? patientRegId;
  final String? age;
  final String? gender;
  final String? dateOfBirth;
  final String? address;
  final String? state;
  final String? stateId;
  final String? city;
  final String? cityId;
  final String? country;
  final String? countryId;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  PatientRegistration({
    this.id,
    this.firstName,
    this.lastName,
    this.name,
    this.email,
    this.mobile,
    this.patientRegId,
    this.age,
    this.gender,
    this.dateOfBirth,
    this.address,
    this.state,
    this.stateId,
    this.city,
    this.cityId,
    this.country,
    this.countryId,
    this.createdAt,
    this.updatedAt,
  });

  factory PatientRegistration.fromJson(Map<String, dynamic> json) {
    return PatientRegistration(
      id: json['id']?.toString(),
      firstName: json['first_name']?.toString(),
      lastName: json['last_name']?.toString(),
      name: json['name']?.toString(), // This comes from the API as combined name
      email: json['email']?.toString(),
      mobile: json['mobile']?.toString(),
      patientRegId: json['patient_reg_id']?.toString(),
      age: json['age']?.toString(),
      gender: json['gender']?.toString(),
      dateOfBirth: json['date_of_birth']?.toString(),
      address: json['address']?.toString(),
      state: json['state']?.toString(),
      stateId: json['state_id']?.toString(),
      city: json['city']?.toString(),
      cityId: json['city_id']?.toString(),
      country: json['country']?.toString(),
      countryId: json['country_id']?.toString(),
      createdAt: json['createdAt'] != null 
          ? DateTime.tryParse(json['createdAt'].toString())
          : null,
      updatedAt: json['updatedAt'] != null 
          ? DateTime.tryParse(json['updatedAt'].toString())
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'first_name': firstName,
      'last_name': lastName,
      'name': name,
      'email': email,
      'mobile': mobile,
      'patient_reg_id': patientRegId,
      'age': age,
      'gender': gender,
      'date_of_birth': dateOfBirth,
      'address': address,
      'state': state,
      'state_id': stateId,
      'city': city,
      'city_id': cityId,
      'country': country,
      'country_id': countryId,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  // Helper method to get age/gender display format
  String get ageGenderDisplay {
    String ageStr = age ?? 'N/A';
    String genderStr = 'N/A';

    if (gender?.isNotEmpty == true) {
      String genderLower = gender!.toLowerCase();
      if (genderLower == 'male' || genderLower == 'm') {
        genderStr = 'M';
      } else if (genderLower == 'female' || genderLower == 'f') {
        genderStr = 'F';
      } else {
        genderStr = gender!.substring(0, 1).toUpperCase();
      }
    }

    return '$ageStr/$genderStr';
  }

  // Helper method to get formatted registration date
  String get formattedRegistrationDate {
    if (createdAt == null) return 'N/A';
    return '${createdAt!.day.toString().padLeft(2, '0')}/${createdAt!.month.toString().padLeft(2, '0')}/${createdAt!.year}';
  }

  // Helper method to get display location (prioritize names over IDs)
  String get displayCountry {
    if (country?.isNotEmpty == true) return country!;
    if (countryId?.isNotEmpty == true) return countryId!;
    return 'N/A';
  }

  String get displayState {
    if (state?.isNotEmpty == true) return state!;
    if (stateId?.isNotEmpty == true) return stateId!;
    return 'N/A';
  }

  String get displayCity {
    if (city?.isNotEmpty == true) return city!;
    if (cityId?.isNotEmpty == true) return cityId!;
    return 'N/A';
  }

  // Helper method to get full location display
  String get fullLocationDisplay {
    List<String> locationParts = [];

    String cityDisplay = displayCity;
    String stateDisplay = displayState;
    String countryDisplay = displayCountry;

    if (cityDisplay != 'N/A') locationParts.add(cityDisplay);
    if (stateDisplay != 'N/A') locationParts.add(stateDisplay);
    if (countryDisplay != 'N/A') locationParts.add(countryDisplay);

    return locationParts.isNotEmpty ? locationParts.join(', ') : 'N/A';
  }

  // Helper method to get compact location display (for table view)
  String get compactLocationDisplay {
    String cityDisplay = displayCity;
    String stateDisplay = displayState;

    // Show city and state only for compact view
    if (cityDisplay != 'N/A' && stateDisplay != 'N/A') {
      return '$cityDisplay, $stateDisplay';
    } else if (cityDisplay != 'N/A') {
      return cityDisplay;
    } else if (stateDisplay != 'N/A') {
      return stateDisplay;
    } else {
      return displayCountry;
    }
  }
}

class Pagination {
  final int total;
  final int page;
  final int limit;
  final int totalPages;

  Pagination({
    required this.total,
    required this.page,
    required this.limit,
    required this.totalPages,
  });

  factory Pagination.fromJson(Map<String, dynamic> json) {
    return Pagination(
      total: json['total'] ?? 0,
      page: json['page'] ?? 1,
      limit: json['limit'] ?? 10,
      totalPages: json['totalPages'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'page': page,
      'limit': limit,
      'totalPages': totalPages,
    };
  }
}

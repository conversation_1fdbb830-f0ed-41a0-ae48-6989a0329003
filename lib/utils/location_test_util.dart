import 'dart:developer' as developer;
import '../services/location_service.dart';
import '../data/models/patient_registration_model.dart';

class LocationTestUtil {
  static Future<void> testLocationResolution() async {
    try {
      final locationService = LocationService();
      await locationService.initialize();
      
      developer.log('=== Location Resolution Test ===');
      
      // Test country resolution
      String countryResult = locationService.getCountryName('IN');
      developer.log('Country IN -> $countryResult');
      
      // Test state resolution
      String stateResult = await locationService.getStateName('AP', 'India');
      developer.log('State AP -> $stateResult');
      
      // Test with sample patient data
      final samplePatient = PatientRegistration(
        id: 'test-123',
        firstName: 'Test',
        lastName: 'Patient',
        countryId: 'IN',
        stateId: 'AP',
        cityId: 'Addanki',
        mobile: '+************',
      );
      
      final resolvedPatient = await locationService.resolveLocationNames(samplePatient);
      
      developer.log('=== Sample Patient Resolution ===');
      developer.log('Original Country ID: ${samplePatient.countryId}');
      developer.log('Resolved Country: ${resolvedPatient.country}');
      developer.log('Original State ID: ${samplePatient.stateId}');
      developer.log('Resolved State: ${resolvedPatient.state}');
      developer.log('City (unchanged): ${resolvedPatient.city}');
      
      developer.log('=== Test Completed Successfully ===');
      
    } catch (e) {
      developer.log('❌ Location Test Failed: $e');
    }
  }
}

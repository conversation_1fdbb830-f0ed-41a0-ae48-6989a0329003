import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/utils/constants/colors.dart';
import 'package:platix/view/screens/dentist/create_reminder_screen.dart';

class RemindersScreen extends StatelessWidget {
  const RemindersScreen({super.key});

  void _navigateToViewReminder({
    required String firstName,
    required String lastName,
    required String date,
    String? mobileNumber,
    String? reminderTime,
    String? reminderText,
  }) {
    Get.to(() => CreateReminderScreen(
      mode: ReminderScreenMode.view,
      showSearchOption: false,
      firstName: firstName,
      lastName: lastName,
      mobileNumber: mobileNumber ?? '+91 XXXXXXXXXX',
      reminderTime: reminderTime ?? '10:24 AM',
      reminderDate: date,
      reminderText: reminderText ?? 'Sample reminder text for $firstName $lastName',
    ));
  }

  void _showDeleteConfirmation({
    required String firstName,
    required String lastName,
  }) {
    Get.defaultDialog(
      title: 'Delete Reminder',
      titleStyle: const TextStyle(
        color: AppColors.primary,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
      middleText: 'Are you sure you want to delete reminder for $firstName $lastName?',
      middleTextStyle: const TextStyle(
        color: AppColors.black,
        fontSize: 14,
      ),
      backgroundColor: AppColors.white,
      radius: 12,
      textConfirm: 'Delete',
      textCancel: 'Cancel',
      confirmTextColor: Colors.white,
      cancelTextColor: AppColors.primary,
      buttonColor: AppColors.primary,
      onConfirm: () {
        Get.back();
        Get.snackbar('Success', 'Reminder for $firstName $lastName deleted successfully!');
      },
      onCancel: () {
        Get.back();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final PermissionService _permissionService = PermissionService();
    return Visibility(
      visible: _permissionService.hasAnyPermission('reminders', ['is_view', 'is_list', 'is_add']),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Reminders'),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Create Reminders',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 20),
                Visibility(
                  visible: _permissionService.hasPermission('reminders', 'is_list'),
                  child: DataTable(
                    columnSpacing: 10,
                    horizontalMargin: 10,
                    headingRowColor: MaterialStateProperty.all(AppColors.primary),
                    headingTextStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                    columns: const [
                      DataColumn(label: Text('S.No')),
                      DataColumn(label: Text('Patient Name')),
                      DataColumn(label: Text('Date')),
                      DataColumn(label: Text('Edit')),
                      DataColumn(label: Text('Delete')),
                    ],
                    rows: [
                      DataRow(
                        color: MaterialStateProperty.all(AppColors.primary2),
                        cells: [
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'Matthew',
                                lastName: 'Thomas',
                                date: '30/10/2020',
                              ),
                              child: const Text('1'),
                            ),
                          ),
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'Matthew',
                                lastName: 'Thomas',
                                date: '30/10/2020',
                              ),
                              child: const Text('Matthew Thomas'),
                            ),
                          ),
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'Matthew',
                                lastName: 'Thomas',
                                date: '30/10/2020',
                              ),
                              child: const Text('30/10/2020'),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('reminders', 'is_edit'),
                              child: IconButton(
                                icon: const Icon(Icons.edit, color: AppColors.primary),
                                onPressed: () {
                                  Get.to(() => const CreateReminderScreen(
                                    mode: ReminderScreenMode.edit,
                                    showSearchOption: false,
                                  ));
                                }
                              ),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('reminders', 'is_delete'),
                              child: IconButton(
                                icon: const Icon(Icons.delete, color: AppColors.primary),
                                onPressed: () {
                                  _showDeleteConfirmation(
                                    firstName: 'Matthew',
                                    lastName: 'Thomas',
                                  );
                                }
                              ),
                            ),
                          ),
                        ],
                      ),
                      DataRow(
                        color: MaterialStateProperty.all(Colors.white),
                        cells: [
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'John',
                                lastName: 'Davis',
                                date: '16/10/2021',
                              ),
                              child: const Text('2'),
                            ),
                          ),
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'John',
                                lastName: 'Davis',
                                date: '16/10/2021',
                              ),
                              child: const Text('John Davis'),
                            ),
                          ),
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'John',
                                lastName: 'Davis',
                                date: '16/10/2021',
                              ),
                              child: const Text('16/10/2021'),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('reminders', 'is_edit'),
                              child: IconButton(
                                icon: const Icon(Icons.edit, color: AppColors.primary),
                                onPressed: () {
                                  Get.to(() => const CreateReminderScreen(
                                    mode: ReminderScreenMode.edit,
                                    showSearchOption: false,
                                  ));
                                }
                              ),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('reminders', 'is_delete'),
                              child: IconButton(
                                icon: const Icon(Icons.delete, color: AppColors.primary),
                                onPressed: () {
                                  _showDeleteConfirmation(
                                    firstName: 'John',
                                    lastName: 'Davis',
                                  );
                                }
                              ),
                            ),
                          ),
                        ],
                      ),
                      DataRow(
                        color: MaterialStateProperty.all(AppColors.primary2),
                        cells: [
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'Elizabeth',
                                lastName: 'Jones',
                                date: '10/01/2024',
                              ),
                              child: const Text('3'),
                            ),
                          ),
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'Elizabeth',
                                lastName: 'Jones',
                                date: '10/01/2024',
                              ),
                              child: const Text('Elizabeth Jones'),
                            ),
                          ),
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'Elizabeth',
                                lastName: 'Jones',
                                date: '10/01/2024',
                              ),
                              child: const Text('10/01/2024'),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('reminders', 'is_edit'),
                              child: IconButton(
                                icon: const Icon(Icons.edit, color: AppColors.primary),
                                onPressed: () {
                                  Get.to(() => const CreateReminderScreen(
                                    mode: ReminderScreenMode.edit,
                                    showSearchOption: false,
                                  ));
                                }
                              ),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('reminders', 'is_delete'),
                              child: IconButton(
                                icon: const Icon(Icons.delete, color: AppColors.primary),
                                onPressed: () {
                                  _showDeleteConfirmation(
                                    firstName: 'Elizabeth',
                                    lastName: 'Jones',
                                  );
                                }
                              ),
                            ),
                          ),
                        ],
                      ),
                      DataRow(
                        color: MaterialStateProperty.all(Colors.white),
                        cells: [
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'Ryan',
                                lastName: 'Young',
                                date: '09/01/2022',
                              ),
                              child: const Text('4'),
                            ),
                          ),
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'Ryan',
                                lastName: 'Young',
                                date: '09/01/2022',
                              ),
                              child: const Text('Ryan Young'),
                            ),
                          ),
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'Ryan',
                                lastName: 'Young',
                                date: '09/01/2022',
                              ),
                              child: const Text('09/01/2022'),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('reminders', 'is_edit'),
                              child: IconButton(
                                icon: const Icon(Icons.edit, color: AppColors.primary),
                                onPressed: () {
                                  Get.to(() => const CreateReminderScreen(
                                    mode: ReminderScreenMode.edit,
                                    showSearchOption: false,
                                  ));
                                }
                              ),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('reminders', 'is_delete'),
                              child: IconButton(
                                icon: const Icon(Icons.delete, color: AppColors.primary),
                                onPressed: () {
                                  _showDeleteConfirmation(
                                    firstName: 'Ryan',
                                    lastName: 'Young',
                                  );
                                }
                              ),
                            ),
                          ),
                        ],
                      ),
                      DataRow(
                        color: MaterialStateProperty.all(AppColors.primary2),
                        cells: [
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'Anthony',
                                lastName: 'Moore',
                                date: '30/01/2025',
                              ),
                              child: const Text('5'),
                            ),
                          ),
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'Anthony',
                                lastName: 'Moore',
                                date: '30/01/2025',
                              ),
                              child: const Text('Anthony Moore'),
                            ),
                          ),
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'Anthony',
                                lastName: 'Moore',
                                date: '30/01/2025',
                              ),
                              child: const Text('30/01/2025'),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('reminders', 'is_edit'),
                              child: IconButton(
                                icon: const Icon(Icons.edit, color: AppColors.primary),
                                onPressed: () {
                                  Get.to(() => const CreateReminderScreen(
                                    mode: ReminderScreenMode.edit,
                                    showSearchOption: false,
                                  ));
                                }
                              ),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('reminders', 'is_delete'),
                              child: IconButton(
                                icon: const Icon(Icons.delete, color: AppColors.primary),
                                onPressed: () {
                                  _showDeleteConfirmation(
                                    firstName: 'Anthony',
                                    lastName: 'Moore',
                                  );
                                }
                              ),
                            ),
                          ),
                        ],
                      ),
                      DataRow(
                        color: MaterialStateProperty.all(Colors.white),
                        cells: [
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'Nicole',
                                lastName: 'Davis',
                                date: '23/01/2024',
                              ),
                              child: const Text('6'),
                            ),
                          ),
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'Nicole',
                                lastName: 'Davis',
                                date: '23/01/2024',
                              ),
                              child: const Text('Nicole Davis'),
                            ),
                          ),
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'Nicole',
                                lastName: 'Davis',
                                date: '23/01/2024',
                              ),
                              child: const Text('23/01/2024'),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('reminders', 'is_edit'),
                              child: IconButton(
                                icon: const Icon(Icons.edit, color: AppColors.primary),
                                onPressed: () {
                                  Get.to(() => const CreateReminderScreen(
                                    mode: ReminderScreenMode.edit,
                                    showSearchOption: false,
                                  ));
                                }
                              ),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('reminders', 'is_delete'),
                              child: IconButton(
                                icon: const Icon(Icons.delete, color: AppColors.primary),
                                onPressed: () {
                                  _showDeleteConfirmation(
                                    firstName: 'Nicole',
                                    lastName: 'Davis',
                                  );
                                }
                              ),
                            ),
                          ),
                        ],
                      ),
                      DataRow(
                        color: MaterialStateProperty.all(AppColors.primary2),
                        cells: [
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'Stepane',
                                lastName: 'Johnson',
                                date: '19/02/2024',
                              ),
                              child: const Text('7'),
                            ),
                          ),
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'Stepane',
                                lastName: 'Johnson',
                                date: '19/02/2024',
                              ),
                              child: const Text('Stepane Johnson'),
                            ),
                          ),
                          DataCell(
                            InkWell(
                              onTap: () => _navigateToViewReminder(
                                firstName: 'Stepane',
                                lastName: 'Johnson',
                                date: '19/02/2024',
                              ),
                              child: const Text('19/02/2024'),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('reminders', 'is_edit'),
                              child: IconButton(
                                icon: const Icon(Icons.edit, color: AppColors.primary),
                                onPressed: () {
                                  Get.to(() => const CreateReminderScreen(
                                    mode: ReminderScreenMode.edit,
                                    showSearchOption: false,
                                  ));
                                }
                              ),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('reminders', 'is_delete'),
                              child: IconButton(
                                icon: const Icon(Icons.delete, color: AppColors.primary),
                                onPressed: () {
                                  _showDeleteConfirmation(
                                    firstName: 'Stepane',
                                    lastName: 'Johnson',
                                  );
                                }
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: Visibility(
          visible: _permissionService.hasPermission('reminders', 'is_add'),
          child: FloatingActionButton(
            onPressed: () {
              Get.to(() => const CreateReminderScreen(
                mode: ReminderScreenMode.create,
                showSearchOption: true,
              ));
            },
            child: const Icon(Icons.add),
          ),
        ),
      ),
    );
  }
}

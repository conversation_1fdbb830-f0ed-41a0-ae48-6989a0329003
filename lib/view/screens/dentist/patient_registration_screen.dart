import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/controllers/dentist_controllers/patient_registration_controller.dart';
import 'package:platix/data/models/patient_registration_model.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/dentist/create_patient_registration_screen.dart';

class PatientRegistrationScreen extends StatefulWidget {
  const PatientRegistrationScreen({super.key});

  @override
  State<PatientRegistrationScreen> createState() => _PatientRegistrationScreenState();
}

class _PatientRegistrationScreenState extends State<PatientRegistrationScreen> {
  late PatientRegistrationController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(PatientRegistrationController());
  }

  @override
  Widget build(BuildContext context) {
    final PermissionService permissionService = PermissionService();
    return Visibility(
      visible: permissionService.hasAnyPermission('Registration', ['is_view', 'is_list', 'is_add']),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Patient Registration'),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Search Bar
                Visibility(
                  visible: permissionService.hasPermission('Registration', 'is_list'),
                  child: TextField(
                    controller: controller.searchController,
                    decoration: InputDecoration(
                      hintText: 'Search by name, email, mobile, or patient ID...',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: GetBuilder<PatientRegistrationController>(
                        builder: (controller) => controller.searchController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: controller.clearSearch,
                              )
                            : const SizedBox.shrink(),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onChanged: (value) {
                      // Debounce search to avoid too many API calls
                      if (value.isEmpty) {
                        controller.clearSearch();
                      }
                    },
                    onSubmitted: (value) {
                      controller.searchPatientRegistrations(value);
                    },
                  ),
                ),
                const SizedBox(height: 20),

                const Text(
                  'Patient Registration List',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 20),
                // Patient Registration Data Table
                Visibility(
                  visible: permissionService.hasPermission('Registration', 'is_list'),
                  child: GetBuilder<PatientRegistrationController>(
                    builder: (controller) {
                      if (controller.isLoading && controller.patientRegistrations.isEmpty) {
                        return const Center(
                          child: Padding(
                            padding: EdgeInsets.all(20.0),
                            child: CircularProgressIndicator(),
                          ),
                        );
                      }

                      // Show location resolution indicator
                      if (controller.isResolvingLocations) {
                        return const Center(
                          child: Padding(
                            padding: EdgeInsets.all(20.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                CircularProgressIndicator(),
                                SizedBox(height: 10),
                                Text(
                                  'Resolving location names...',
                                  style: TextStyle(color: Colors.grey),
                                ),
                              ],
                            ),
                          ),
                        );
                      }

                      if (controller.patientRegistrations.isEmpty) {
                        return const Center(
                          child: Padding(
                            padding: EdgeInsets.all(20.0),
                            child: Text(
                              'No patient registrations found',
                              style: TextStyle(fontSize: 16, color: Colors.grey),
                            ),
                          ),
                        );
                      }

                      return Column(
                        children: [
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columnSpacing: 10,
                              horizontalMargin: 10,
                              headingRowColor: WidgetStateProperty.all(AppColors.primary),
                              headingTextStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                              columns: const [
                                DataColumn(label: Text('S.No')),
                                DataColumn(label: Text('Patient Name')),
                                DataColumn(label: Text('Age/Gender')),
                                DataColumn(label: Text('Location')),
                                DataColumn(label: Text('Registration Date')),
                                DataColumn(label: Text('Edit')),
                                DataColumn(label: Text('Delete')),
                              ],
                              rows: controller.patientRegistrations.asMap().entries.map((entry) {
                                int index = entry.key;
                                PatientRegistration patient = entry.value;

                                return DataRow(
                                  color: WidgetStateProperty.all(
                                    index % 2 == 0 ? AppColors.primary2 : Colors.white
                                  ),
                                  cells: [
                                    DataCell(Text('${index + 1}')),
                                    DataCell(Text(patient.name ?? 'N/A')),
                                    DataCell(Text(patient.ageGenderDisplay)),
                                    DataCell(
                                      Container(
                                        constraints: const BoxConstraints(maxWidth: 120),
                                        child: Tooltip(
                                          message: patient.fullLocationDisplay,
                                          child: Text(
                                            patient.compactLocationDisplay,
                                            style: const TextStyle(fontSize: 12),
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 1,
                                          ),
                                        ),
                                      ),
                                    ),
                                    DataCell(Text(patient.formattedRegistrationDate)),
                                    DataCell(
                                      Visibility(
                                        visible: permissionService.hasPermission('Registration', 'is_edit'),
                                        child: IconButton(
                                          icon: const Icon(Icons.edit, color: AppColors.primary),
                                          onPressed: () {
                                            Get.to(() => CreatePatientRegistrationScreen(
                                              showSearchOption: false,
                                              patientData: patient,
                                            ));
                                          },
                                        ),
                                      ),
                                    ),
                                    DataCell(
                                      Visibility(
                                        visible: permissionService.hasPermission('Registration', 'is_delete'),
                                        child: IconButton(
                                          icon: const Icon(Icons.delete, color: AppColors.primary),
                                          onPressed: () {
                                            Get.defaultDialog(
                                              title: 'Delete Patient',
                                              titleStyle: const TextStyle(
                                                color: AppColors.primary,
                                                fontSize: 18,
                                                fontWeight: FontWeight.bold,
                                              ),
                                              middleText: 'Are you sure you want to delete patient ${patient.name ?? 'this patient'}?',
                                              middleTextStyle: const TextStyle(
                                                color: AppColors.black,
                                                fontSize: 14,
                                              ),
                                              backgroundColor: AppColors.white,
                                              radius: 12,
                                              textConfirm: 'Delete',
                                              textCancel: 'Cancel',
                                              confirmTextColor: Colors.white,
                                              cancelTextColor: AppColors.primary,
                                              buttonColor: AppColors.primary,
                                              onConfirm: () {
                                                Get.back();
                                                if (patient.id != null) {
                                                  controller.deletePatientRegistration(patient.id!);
                                                }
                                              },
                                              onCancel: () {
                                                Get.back();
                                              },
                                            );
                                          },
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              }).toList(),
                            ),
                          ),

                          // Pagination and Load More
                          if (controller.pagination != null)
                            Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Total: ${controller.pagination!.total} patients',
                                    style: const TextStyle(fontSize: 14, color: Colors.grey),
                                  ),
                                  Text(
                                    'Page ${controller.pagination!.page} of ${controller.pagination!.totalPages}',
                                    style: const TextStyle(fontSize: 14, color: Colors.grey),
                                  ),
                                ],
                              ),
                            ),

                          // Load More Button
                          if (controller.hasMoreData && !controller.isLoadingMore)
                            Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: CustomElevatedButton(
                                onPressed: controller.loadMorePatientRegistrations,
                                text: 'Load More',
                                buttonStyle: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.primary,
                                  foregroundColor: AppColors.white,
                                ),
                              ),
                            ),

                          // Loading More Indicator
                          if (controller.isLoadingMore)
                            const Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Center(child: CircularProgressIndicator()),
                            ),
                        ],
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: Visibility(
          visible: permissionService.hasPermission('Registration', 'is_add'),
          child: FloatingActionButton(
            onPressed: () {
              Get.to(() => const CreatePatientRegistrationScreen(
                showSearchOption: true,
                patientData: null, // Explicitly null for create mode
              ));
            },
            backgroundColor: AppColors.primary,
            child: const Icon(Icons.add, color: Colors.white),
          ),
        ),
      ),
    );
  }
}

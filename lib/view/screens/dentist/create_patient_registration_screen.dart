import 'package:platix/data/models/patient_registration_model.dart';
import 'package:platix/services/permission_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/utils/app_export.dart';
import 'package:intl/intl.dart';
import 'package:country_state_city/country_state_city.dart' as csc;
import 'package:dropdown_search/dropdown_search.dart';
import 'dart:developer' as developer;

class CreatePatientRegistrationScreen extends StatefulWidget {
  final bool showSearchOption;
  final PatientRegistration? patientData; // For edit mode

  const CreatePatientRegistrationScreen({
    super.key,
    this.showSearchOption = true,
    this.patientData,
  });

  @override
  State<CreatePatientRegistrationScreen> createState() =>
      _CreatePatientRegistrationScreenState();
}

class _CreatePatientRegistrationScreenState extends State<CreatePatientRegistrationScreen> {
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController mobileNumController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController ageController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  String? _selectedGender;
  DateTime? _selectedDateOfBirth;

  // Location selection variables for country_state_city package
  LocationItem? _selectedCountry;
  LocationItem? _selectedState;
  LocationItem? _selectedCity;
  List<LocationItem> _countries = [];
  List<LocationItem> _states = [];
  List<LocationItem> _cities = [];
  bool _isStateEnabled = false;
  bool _isCityEnabled = false;

  final PermissionService _permissionService = PermissionService();

  // Helper methods to get location data for API submission
  Map<String, String?> getLocationData() {
    return {
      'country': _selectedCountry?.name,
      'country_id': _selectedCountry?.id,
      'state': _selectedState?.name,
      'state_id': _selectedState?.id,
      'city': _selectedCity?.name,
      'city_id': _selectedCity?.id,
    };
  }

  // Helper method to find LocationItem by name (for backward compatibility)
  LocationItem? findLocationByName(List<LocationItem> items, String? name) {
    if (name == null || name.isEmpty) return null;
    try {
      return items.firstWhere(
        (item) => item.name.toLowerCase() == name.toLowerCase(),
      );
    } catch (e) {
      // If not found, create a new LocationItem with the name
      return LocationItem(id: '', name: name);
    }
  }

  @override
  void initState() {
    super.initState();
    _initializeLocationData();
    _initializeFormData();
  }

  Future<void> _initializeLocationData() async {
    try {
      // Load countries
      final countries = await csc.getAllCountries();
      _countries = countries.map((country) => LocationItem.fromCountryStateCity(country)).toList();

      // Set India as default country
      _selectedCountry = _countries.firstWhere(
        (country) => country.name.toLowerCase() == 'india',
        orElse: () => _countries.isNotEmpty ? _countries.first : LocationItem(id: 'IN', name: 'India'),
      );

      // Load states for India by default
      if (_selectedCountry != null) {
        final states = await csc.getStatesOfCountry(_selectedCountry!.name);
        _states = states.map((state) => LocationItem.fromCountryStateCity(state)).toList();
        _isStateEnabled = true;
      }

      setState(() {});
    } catch (e) {
      // Handle error - fallback to basic implementation
      _countries = [
        LocationItem(id: 'IN', name: 'India'),
        LocationItem(id: 'US', name: 'United States'),
        LocationItem(id: 'GB', name: 'United Kingdom'),
        LocationItem(id: 'CA', name: 'Canada'),
        LocationItem(id: 'AU', name: 'Australia'),
      ];
      _selectedCountry = _countries.first; // India
      _isStateEnabled = false;
      setState(() {});
    }
  }

  Future<void> _initializeFormData() async {
    if (widget.patientData != null) {
      // Pre-populate form fields with existing patient data for edit mode
      final patient = widget.patientData!;

      firstNameController.text = patient.firstName ?? '';
      lastNameController.text = patient.lastName ?? '';
      mobileNumController.text = patient.mobile ?? '';
      emailController.text = patient.email ?? '';
      ageController.text = patient.age ?? '';
      addressController.text = patient.address ?? '';

      // Set gender with proper mapping
      if (patient.gender != null && patient.gender!.isNotEmpty) {
        String genderValue = patient.gender!.toLowerCase();
        if (genderValue == 'm' || genderValue == 'male') {
          _selectedGender = 'Male';
        } else if (genderValue == 'f' || genderValue == 'female') {
          _selectedGender = 'Female';
        }
      }

      // Set date of birth
      if (patient.dateOfBirth != null && patient.dateOfBirth!.isNotEmpty) {
        try {
          // Assuming the date format from API is ISO string or similar
          _selectedDateOfBirth = DateTime.tryParse(patient.dateOfBirth!);
        } catch (e) {
          // If parsing fails, try different formats
          try {
            _selectedDateOfBirth = DateFormat('dd/MM/yyyy').parse(patient.dateOfBirth!);
          } catch (e) {
            // If all parsing fails, leave it null
            _selectedDateOfBirth = null;
          }
        }
      }

      // Set location data and load dependent dropdowns
      String countryName = patient.country ?? "India";
      String? countryId = patient.countryId;

      // Find or create the country LocationItem
      _selectedCountry = _countries.firstWhere(
        (country) => country.name.toLowerCase() == countryName.toLowerCase() ||
                    (countryId != null && country.id == countryId),
        orElse: () => LocationItem(id: countryId ?? 'IN', name: countryName),
      );

      // Load states for the selected country
      if (_selectedCountry != null) {
        try {
          final states = await csc.getStatesOfCountry(_selectedCountry!.name);
          _states = states.map((state) => LocationItem.fromCountryStateCity(state)).toList();
          _isStateEnabled = true;

          // Set the selected state if available
          String? stateName = patient.state;
          String? stateId = patient.stateId;

          if (stateName != null) {
            _selectedState = _states.firstWhere(
              (state) => state.name.toLowerCase() == stateName.toLowerCase() ||
                        (stateId != null && state.id == stateId),
              orElse: () => LocationItem(id: stateId ?? '', name: stateName),
            );

            // Load cities for the selected state
            if (_selectedState != null) {
              // Use fallback cities for now
              List<LocationItem> cities = [];
              if (_selectedState!.name.toLowerCase().contains('maharashtra')) {
                cities = [
                  LocationItem(id: 'mumbai', name: 'Mumbai'),
                  LocationItem(id: 'pune', name: 'Pune'),
                  LocationItem(id: 'nagpur', name: 'Nagpur'),
                  LocationItem(id: 'nashik', name: 'Nashik'),
                  LocationItem(id: 'aurangabad', name: 'Aurangabad'),
                ];
              } else if (_selectedState!.name.toLowerCase().contains('delhi')) {
                cities = [
                  LocationItem(id: 'new_delhi', name: 'New Delhi'),
                  LocationItem(id: 'delhi', name: 'Delhi'),
                  LocationItem(id: 'gurgaon', name: 'Gurgaon'),
                  LocationItem(id: 'noida', name: 'Noida'),
                  LocationItem(id: 'faridabad', name: 'Faridabad'),
                ];
              } else {
                cities = [
                  LocationItem(id: 'city1', name: 'City 1'),
                  LocationItem(id: 'city2', name: 'City 2'),
                  LocationItem(id: 'city3', name: 'City 3'),
                ];
              }

              _cities = cities;
              _isCityEnabled = true;

              // Set the selected city if available
              String? cityName = patient.city;
              String? cityId = patient.cityId;

              if (cityName != null) {
                _selectedCity = _cities.firstWhere(
                  (city) => city.name.toLowerCase() == cityName.toLowerCase() ||
                           (cityId != null && city.id == cityId),
                  orElse: () => LocationItem(id: cityId ?? '', name: cityName),
                );
              }
            }
          }
        } catch (e) {
          // Handle error
          _states.clear();
          _isStateEnabled = false;
        }
      }

      setState(() {});
    }
  }

  @override
  void dispose() {
    firstNameController.dispose();
    lastNameController.dispose();
    mobileNumController.dispose();
    emailController.dispose();
    ageController.dispose();
    addressController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDateOfBirth ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDateOfBirth) {
      setState(() {
        _selectedDateOfBirth = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.patientData != null ? 'Edit Patient Registration' : 'New Patient Registration'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.showSearchOption)
              Visibility(
                visible: _permissionService.hasPermission('Registration', 'is_list'),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: 'Search Patient',
                                style: CustomTextStyles.b4_1,
                              ),
                              TextSpan(
                                text: "*",
                                style: CustomTextStyles.b4Primary,
                              ),
                            ],
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    LabelTextField(
                      label: '',
                      hint: 'Search Patient',
                      controller: TextEditingController(),
                      suffix: const Icon(Icons.search),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            const SizedBox(height: 20),
            LabelTextField(
              label: 'First Name',
              hint: 'Enter First Name',
              controller: firstNameController,
            ),
            const SizedBox(height: 15),
            LabelTextField(
              label: 'Last Name',
              hint: 'Enter Last Name',
              controller: lastNameController,
            ),
            const SizedBox(height: 15),
            LabelTextField(
              label: 'Mobile Num',
              hint: 'Enter Mobile Number',
              controller: mobileNumController,
              inputType: TextInputType.phone,
            ),
            const SizedBox(height: 15),
            LabelTextField(
              label: 'Email',
              hint: 'Enter Email',
              controller: emailController,
              inputType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 15),
            Text(
              'Gender',
              style: CustomTextStyles.b4_1,
            ),
            Row(
              children: [
                Radio<String>(
                  value: 'Male',
                  groupValue: _selectedGender,
                  onChanged: (String? value) {
                    setState(() {
                      _selectedGender = value;
                    });
                  },
                  activeColor: AppColors.primary,
                ),
                const Text('Male'),
                const SizedBox(width: 20),
                Radio<String>(
                  value: 'Female',
                  groupValue: _selectedGender,
                  onChanged: (String? value) {
                    setState(() {
                      _selectedGender = value;
                    });
                  },
                  activeColor: AppColors.primary,
                ),
                const Text('Female'),
              ],
            ),
            const SizedBox(height: 15),
            Row(
              children: [
                Expanded(
                  child: LabelTextField(
                    label: 'Age',
                    hint: 'Enter Age',
                    controller: ageController,
                    inputType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: GestureDetector(
                    onTap: () => _selectDate(context),
                    child: AbsorbPointer(
                      child: LabelTextField(
                        label: 'DOB.',
                        hint: _selectedDateOfBirth == null
                            ? 'Select Date'
                            : DateFormat('dd/MM/yyyy')
                                .format(_selectedDateOfBirth!),
                        controller: TextEditingController(
                            text: _selectedDateOfBirth == null
                                ? ''
                                : DateFormat('dd/MM/yyyy')
                                    .format(_selectedDateOfBirth!)),
                        suffix: const Icon(Icons.calendar_today),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            LabelTextField(
              label: 'Address',
              hint: 'Enter Address',
              controller: addressController,
              maxLines: 3,
            ),
            const SizedBox(height: 15),
            // Country Dropdown
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Country',
                  style: CustomTextStyles.b4_1,
                ),
                const SizedBox(height: 8),
                DropdownSearch<LocationItem>(
                  items: _countries,
                  selectedItem: _selectedCountry,
                  itemAsString: (LocationItem item) => item.name,
                  dropdownDecoratorProps: DropDownDecoratorProps(
                    dropdownSearchDecoration: InputDecoration(
                      hintText: 'Search and select Country',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: AppColors.grey),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: AppColors.grey),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: AppColors.primary),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                    ),
                  ),
                  popupProps: PopupProps.menu(
                    showSearchBox: true,
                    searchDelay: const Duration(milliseconds: 300), // Debouncing
                    searchFieldProps: const TextFieldProps(
                      decoration: InputDecoration(
                        hintText: 'Search countries...',
                        prefixIcon: Icon(Icons.search),
                        border: OutlineInputBorder(),
                      ),
                    ),
                    emptyBuilder: (context, searchEntry) => const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Text(
                        'No countries found',
                        style: TextStyle(color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    itemBuilder: (context, item, isSelected) => ListTile(
                      title: Text(item.name),
                      selected: isSelected,
                    ),
                  ),
                  filterFn: (item, filter) => item.name.toLowerCase().contains(filter.toLowerCase()),
                  onChanged: (LocationItem? newValue) async {
                    if (newValue != null) {
                      setState(() {
                        _selectedCountry = newValue;
                        _selectedState = null;
                        _selectedCity = null;
                        _states.clear();
                        _cities.clear();
                        _isStateEnabled = false;
                        _isCityEnabled = false;
                      });

                      // Load states for selected country
                      try {
                        final states = await csc.getStatesOfCountry(newValue.name);
                        setState(() {
                          _states = states.map((state) => LocationItem.fromCountryStateCity(state)).toList();
                          _isStateEnabled = true;
                        });
                      } catch (e) {
                        // Handle error
                        setState(() {
                          _states.clear();
                          _isStateEnabled = false;
                        });
                      }
                    }
                  },
                ),
              ],
            ),
            const SizedBox(height: 20),

            // State Dropdown
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'State',
                  style: CustomTextStyles.b4_1,
                ),
                const SizedBox(height: 8),
                DropdownSearch<LocationItem>(
                  items: _states,
                  selectedItem: _selectedState,
                  enabled: _isStateEnabled,
                  itemAsString: (LocationItem item) => item.name,
                  dropdownDecoratorProps: DropDownDecoratorProps(
                    dropdownSearchDecoration: InputDecoration(
                      hintText: 'Search and select State',
                      hintStyle: TextStyle(
                        color: _isStateEnabled ? Colors.grey : Colors.grey.withOpacity(0.5),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: _isStateEnabled ? AppColors.grey : AppColors.grey.withOpacity(0.5),
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: _isStateEnabled ? AppColors.grey : AppColors.grey.withOpacity(0.5),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: _isStateEnabled ? AppColors.primary : AppColors.grey.withOpacity(0.5),
                        ),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                      filled: !_isStateEnabled,
                      fillColor: !_isStateEnabled ? Colors.grey.withOpacity(0.1) : null,
                    ),
                  ),
                  popupProps: PopupProps.menu(
                    showSearchBox: true,
                    searchDelay: const Duration(milliseconds: 300), // Debouncing
                    searchFieldProps: const TextFieldProps(
                      decoration: InputDecoration(
                        hintText: 'Search states...',
                        prefixIcon: Icon(Icons.search),
                        border: OutlineInputBorder(),
                      ),
                    ),
                    emptyBuilder: (context, searchEntry) => const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Text(
                        'No states found',
                        style: TextStyle(color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    itemBuilder: (context, item, isSelected) => ListTile(
                      title: Text(item.name),
                      selected: isSelected,
                    ),
                  ),
                  filterFn: (item, filter) => item.name.toLowerCase().contains(filter.toLowerCase()),
                  onChanged: _isStateEnabled
                      ? (LocationItem? newValue) async {
                          if (newValue != null && _selectedCountry != null) {
                            setState(() {
                              _selectedState = newValue;
                              _selectedCity = null;
                              _cities.clear();
                              _isCityEnabled = false;
                            });

                            // Load cities for selected state
                            try {
                              // Use fallback cities for now
                              List<LocationItem> cities = [];
                              if (newValue.name.toLowerCase().contains('maharashtra')) {
                                cities = [
                                  LocationItem(id: 'mumbai', name: 'Mumbai'),
                                  LocationItem(id: 'pune', name: 'Pune'),
                                  LocationItem(id: 'nagpur', name: 'Nagpur'),
                                  LocationItem(id: 'nashik', name: 'Nashik'),
                                  LocationItem(id: 'aurangabad', name: 'Aurangabad'),
                                ];
                              } else if (newValue.name.toLowerCase().contains('delhi')) {
                                cities = [
                                  LocationItem(id: 'new_delhi', name: 'New Delhi'),
                                  LocationItem(id: 'delhi', name: 'Delhi'),
                                  LocationItem(id: 'gurgaon', name: 'Gurgaon'),
                                  LocationItem(id: 'noida', name: 'Noida'),
                                  LocationItem(id: 'faridabad', name: 'Faridabad'),
                                ];
                              } else {
                                cities = [
                                  LocationItem(id: 'city1', name: 'City 1'),
                                  LocationItem(id: 'city2', name: 'City 2'),
                                  LocationItem(id: 'city3', name: 'City 3'),
                                ];
                              }

                              setState(() {
                                _cities = cities;
                                _isCityEnabled = true;
                              });
                            } catch (e) {
                              // Handle error
                              setState(() {
                                _cities.clear();
                                _isCityEnabled = false;
                              });
                            }
                          }
                        }
                      : null,
                ),
              ],
            ),
            const SizedBox(height: 20),

            // City Dropdown
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'City',
                  style: CustomTextStyles.b4_1,
                ),
                const SizedBox(height: 8),
                DropdownSearch<LocationItem>(
                  items: _cities,
                  selectedItem: _selectedCity,
                  enabled: _isCityEnabled,
                  itemAsString: (LocationItem item) => item.name,
                  dropdownDecoratorProps: DropDownDecoratorProps(
                    dropdownSearchDecoration: InputDecoration(
                      hintText: 'Search and select City',
                      hintStyle: TextStyle(
                        color: _isCityEnabled ? Colors.grey : Colors.grey.withOpacity(0.5),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: _isCityEnabled ? AppColors.grey : AppColors.grey.withOpacity(0.5),
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: _isCityEnabled ? AppColors.grey : AppColors.grey.withOpacity(0.5),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: _isCityEnabled ? AppColors.primary : AppColors.grey.withOpacity(0.5),
                        ),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                      filled: !_isCityEnabled,
                      fillColor: !_isCityEnabled ? Colors.grey.withOpacity(0.1) : null,
                    ),
                  ),
                  popupProps: PopupProps.menu(
                    showSearchBox: true,
                    searchDelay: const Duration(milliseconds: 300), // Debouncing
                    searchFieldProps: const TextFieldProps(
                      decoration: InputDecoration(
                        hintText: 'Search cities...',
                        prefixIcon: Icon(Icons.search),
                        border: OutlineInputBorder(),
                      ),
                    ),
                    emptyBuilder: (context, searchEntry) => const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Text(
                        'No cities found',
                        style: TextStyle(color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    itemBuilder: (context, item, isSelected) => ListTile(
                      title: Text(item.name),
                      selected: isSelected,
                    ),
                  ),
                  filterFn: (item, filter) => item.name.toLowerCase().contains(filter.toLowerCase()),
                  onChanged: _isCityEnabled
                      ? (LocationItem? newValue) {
                          setState(() {
                            _selectedCity = newValue;
                          });
                        }
                      : null,
                ),
              ],
            ),
            const SizedBox(height: 30),
            CustomElevatedButton(
              onPressed: () {
                // Get location data for API submission
                Map<String, String?> locationData = getLocationData();

                // Example of how to use the location data
                developer.log('Location Data for API:');
                developer.log('Country: ${locationData['country']} (ID: ${locationData['country_id']})');
                developer.log('State: ${locationData['state']} (ID: ${locationData['state_id']})');
                developer.log('City: ${locationData['city']} (ID: ${locationData['city_id']})');

                // Here you would typically send the data to your API
                // The API should receive both names (for display) and IDs (for database storage)

                String successMessage = widget.patientData != null
                    ? 'Patient Updated Successfully!'
                    : 'Patient Registered Successfully!';
                Get.snackbar('Success', successMessage);
              },
              text: widget.patientData != null ? 'Update' : 'Save',
              buttonStyle: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

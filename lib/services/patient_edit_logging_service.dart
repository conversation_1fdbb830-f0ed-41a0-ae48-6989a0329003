import 'dart:developer' as developer;
import 'package:get/get.dart';
import '../data/models/patient_registration_model.dart';

class PatientEditLoggingService {
  static final PatientEditLoggingService _instance = PatientEditLoggingService._internal();
  factory PatientEditLoggingService() => _instance;
  PatientEditLoggingService._internal();

  /// Log patient edit action for Software Planning MCP
  void logPatientEdit(PatientRegistration patient) {
    try {
      final logData = {
        'timestamp': DateTime.now().toIso8601String(),
        'action': 'patient_edit',
        'patient_id': patient.id,
        'patient_name': '${patient.firstName} ${patient.lastName}',
        'patient_reg_id': patient.patientRegId,
        'mobile': patient.mobile,
        'original_data': {
          'country_code': patient.countryId ?? patient.country,
          'state_code': patient.stateId ?? patient.state,
          'city_code': patient.cityId ?? patient.city,
        },
        'resolved_data': {
          'country_name': patient.country,
          'state_name': patient.state,
          'city_name': patient.city,
        }
      };

      developer.log('Patient Edit Logged: ${logData.toString()}');
      
      // TODO: Integrate with Software Planning MCP
      // This is where we would create todo items in the MCP
      _createMcpTodoItem(patient, logData);
      
    } catch (e) {
      developer.log('Failed to log patient edit: $e');
    }
  }

  /// Create todo item in Software Planning MCP
  void _createMcpTodoItem(PatientRegistration patient, Map<String, dynamic> logData) {
    try {
      // This would integrate with the Software Planning MCP
      // For now, we'll just log the structure that would be sent
      final todoData = {
        'title': 'Patient Edit: ${patient.firstName} ${patient.lastName}',
        'description': '''
Patient Registration Edit Action:
- Patient ID: ${patient.id}
- Registration ID: ${patient.patientRegId}
- Name: ${patient.firstName} ${patient.lastName}
- Mobile: ${patient.mobile}
- Country: ${patient.country} (${patient.countryId})
- State: ${patient.state} (${patient.stateId})
- City: ${patient.city} (${patient.cityId})
- Edited at: ${logData['timestamp']}
        ''',
        'complexity': 3, // Low complexity for edit logging
        'category': 'patient_management',
        'tags': ['edit', 'patient', 'registration'],
        'metadata': logData
      };

      developer.log('MCP Todo Item Created: ${todoData.toString()}');
      
      // TODO: Actual MCP integration would go here
      // Example: await mcpService.createTodo(todoData);
      
    } catch (e) {
      developer.log('Failed to create MCP todo item: $e');
    }
  }

  /// Log batch patient edits
  void logPatientEdits(List<PatientRegistration> patients) {
    for (var patient in patients) {
      logPatientEdit(patient);
    }
    developer.log('Batch logged ${patients.length} patient edits');
  }
}

import 'dart:developer' as developer;
import 'package:country_state_city/country_state_city.dart' as csc;
import '../data/models/patient_registration_model.dart';

class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  // Cache for location mappings to improve performance
  final Map<String, String> _countryCache = {};
  final Map<String, String> _stateCache = {};
  final Map<String, String> _cityCache = {};
  
  bool _isInitialized = false;

  /// Initialize the location service with basic mappings
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Use comprehensive fallback mappings for reliability
      _countryCache.addAll({
        'IN': 'India',
        'US': 'United States',
        'GB': 'United Kingdom',
        'CA': 'Canada',
        'AU': 'Australia',
        'DE': 'Germany',
        'FR': 'France',
        'JP': 'Japan',
        'CN': 'China',
        'BR': 'Brazil',
        'RU': 'Russia',
        'IT': 'Italy',
        'ES': 'Spain',
        'MX': 'Mexico',
        'KR': 'South Korea',
        'ID': 'Indonesia',
        'TR': 'Turkey',
        'SA': 'Saudi Arabia',
        'CH': 'Switzerland',
        'TW': 'Taiwan',
        'BE': 'Belgium',
        'AR': 'Argentina',
        'SE': 'Sweden',
        'IE': 'Ireland',
        'IL': 'Israel',
        'AE': 'United Arab Emirates',
        'MY': 'Malaysia',
        'ZA': 'South Africa',
        'TH': 'Thailand',
        'SG': 'Singapore',
        'PH': 'Philippines',
        'FI': 'Finland',
        'CL': 'Chile',
        'BD': 'Bangladesh',
        'VN': 'Vietnam',
        'DK': 'Denmark',
        'NO': 'Norway',
        'EG': 'Egypt',
        'PK': 'Pakistan',
        'NZ': 'New Zealand',
        'UA': 'Ukraine',
        'MA': 'Morocco',
        'KE': 'Kenya',
        'PE': 'Peru',
        'CZ': 'Czech Republic',
        'RO': 'Romania',
        'NP': 'Nepal',
        'LK': 'Sri Lanka',
        'GH': 'Ghana',
        'UZ': 'Uzbekistan',
        'IQ': 'Iraq',
        'AF': 'Afghanistan',
        'PL': 'Poland',
        'DZ': 'Algeria',
        'SD': 'Sudan',
        'UG': 'Uganda',
      });

      // Try to load from country_state_city package as additional source
      try {
        final countries = await csc.getAllCountries();
        for (var country in countries) {
          try {
            String countryName = country.toString();
            // Extract country code from string representation if possible
            if (countryName.length >= 2) {
              String countryCode = countryName.substring(0, 2).toUpperCase();
              if (!_countryCache.containsKey(countryCode)) {
                _countryCache[countryCode] = countryName;
              }
            }
          } catch (e) {
            // Skip problematic entries
          }
        }
      } catch (e) {
        developer.log('Failed to load from country_state_city package: $e');
      }

      _isInitialized = true;
      developer.log('LocationService initialized with ${_countryCache.length} countries');
    } catch (e) {
      developer.log('LocationService initialization failed: $e');
      _isInitialized = true;
    }
  }

  /// Convert country ID to name
  String getCountryName(String? countryId) {
    if (countryId == null || countryId.isEmpty) return 'N/A';
    return _countryCache[countryId] ?? countryId;
  }

  /// Convert state ID to name (with caching)
  Future<String> getStateName(String? stateId, String? countryName) async {
    if (stateId == null || stateId.isEmpty) return 'N/A';

    // Check cache first
    String cacheKey = '${countryName}_$stateId';
    if (_stateCache.containsKey(cacheKey)) {
      return _stateCache[cacheKey]!;
    }

    // Try fallback mapping first (more reliable)
    String? fallbackName = _getStateFallback(stateId);
    if (fallbackName != null) {
      _stateCache[cacheKey] = fallbackName;
      return fallbackName;
    }

    // Try to load from country_state_city package
    try {
      if (countryName != null && countryName.isNotEmpty) {
        final states = await csc.getStatesOfCountry(countryName);
        for (var state in states) {
          try {
            String stateName = state.toString();
            // Create a simple ID from the name
            String generatedId = stateName.toLowerCase().replaceAll(' ', '_');

            String stateCacheKey = '${countryName}_$generatedId';
            _stateCache[stateCacheKey] = stateName;

            // Also try matching with the original stateId
            if (generatedId == stateId.toLowerCase() ||
                stateName.toLowerCase().contains(stateId.toLowerCase()) ||
                stateId.toLowerCase().contains(stateName.toLowerCase().substring(0, 2))) {
              _stateCache[cacheKey] = stateName;
              return stateName;
            }
          } catch (e) {
            // Skip problematic entries
          }
        }
      }
    } catch (e) {
      developer.log('Failed to load states for $countryName: $e');
    }

    // Return the ID as fallback
    return stateId;
  }

  /// Convert city ID to name (with caching)
  Future<String> getCityName(String? cityId, String? stateName, String? countryName) async {
    if (cityId == null || cityId.isEmpty) return 'N/A';
    
    // Check cache first
    String cacheKey = '${countryName}_${stateName}_$cityId';
    if (_cityCache.containsKey(cacheKey)) {
      return _cityCache[cacheKey]!;
    }
    
    // For performance, use fallback mappings for common cities
    String? fallbackName = _getCityFallback(cityId);
    if (fallbackName != null) {
      _cityCache[cacheKey] = fallbackName;
      return fallbackName;
    }
    
    // Return the ID as fallback
    return cityId;
  }

  /// Resolve all location names for a patient registration
  Future<PatientRegistration> resolveLocationNames(PatientRegistration patient) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    String? resolvedCountry;
    String? resolvedState;
    String? resolvedCity;
    
    // Resolve country name
    if (patient.countryId != null) {
      resolvedCountry = getCountryName(patient.countryId);
    } else if (patient.country != null) {
      resolvedCountry = patient.country;
    }
    
    // Resolve state name
    if (patient.stateId != null) {
      resolvedState = await getStateName(patient.stateId, resolvedCountry);
    } else if (patient.state != null) {
      resolvedState = patient.state;
    }
    
    // Resolve city name
    if (patient.cityId != null) {
      resolvedCity = await getCityName(patient.cityId, resolvedState, resolvedCountry);
    } else if (patient.city != null) {
      resolvedCity = patient.city;
    }
    
    // Return updated patient with resolved names
    return PatientRegistration(
      id: patient.id,
      firstName: patient.firstName,
      lastName: patient.lastName,
      name: patient.name,
      email: patient.email,
      mobile: patient.mobile,
      patientRegId: patient.patientRegId,
      age: patient.age,
      gender: patient.gender,
      dateOfBirth: patient.dateOfBirth,
      address: patient.address,
      state: resolvedState,
      stateId: patient.stateId,
      city: resolvedCity,
      cityId: patient.cityId,
      country: resolvedCountry,
      countryId: patient.countryId,
      createdAt: patient.createdAt,
      updatedAt: patient.updatedAt,
    );
  }

  /// Batch resolve location names for multiple patients
  Future<List<PatientRegistration>> resolveLocationNamesForList(List<PatientRegistration> patients) async {
    if (!_isInitialized) {
      await initialize();
    }

    List<PatientRegistration> resolvedPatients = [];

    // Pre-populate cache with common state mappings for better performance
    _prePopulateStateMappings();

    // Now resolve each patient
    for (PatientRegistration patient in patients) {
      try {
        PatientRegistration resolved = await resolveLocationNames(patient);
        resolvedPatients.add(resolved);
      } catch (e) {
        developer.log('Failed to resolve location for patient ${patient.id}: $e');
        // Add original patient if resolution fails
        resolvedPatients.add(patient);
      }
    }

    developer.log('Resolved locations for ${resolvedPatients.length}/${patients.length} patients');
    return resolvedPatients;
  }

  /// Fallback mappings for common states
  String? _getStateFallback(String stateId) {
    const stateMap = {
      'MH': 'Maharashtra',
      'DL': 'Delhi',
      'KA': 'Karnataka',
      'TN': 'Tamil Nadu',
      'UP': 'Uttar Pradesh',
      'WB': 'West Bengal',
      'RJ': 'Rajasthan',
      'GJ': 'Gujarat',
      'MP': 'Madhya Pradesh',
      'AP': 'Andhra Pradesh',
    };
    return stateMap[stateId.toUpperCase()];
  }

  /// Fallback mappings for common cities
  String? _getCityFallback(String cityId) {
    const cityMap = {
      'mumbai': 'Mumbai',
      'delhi': 'Delhi',
      'new_delhi': 'New Delhi',
      'bangalore': 'Bangalore',
      'chennai': 'Chennai',
      'kolkata': 'Kolkata',
      'pune': 'Pune',
      'hyderabad': 'Hyderabad',
      'ahmedabad': 'Ahmedabad',
      'jaipur': 'Jaipur',
      'surat': 'Surat',
      'lucknow': 'Lucknow',
      'kanpur': 'Kanpur',
      'nagpur': 'Nagpur',
      'indore': 'Indore',
      'thane': 'Thane',
      'bhopal': 'Bhopal',
      'visakhapatnam': 'Visakhapatnam',
      'pimpri_chinchwad': 'Pimpri-Chinchwad',
      'patna': 'Patna',
      'vadodara': 'Vadodara',
      'ghaziabad': 'Ghaziabad',
      'ludhiana': 'Ludhiana',
      'agra': 'Agra',
      'nashik': 'Nashik',
      'faridabad': 'Faridabad',
      'meerut': 'Meerut',
      'rajkot': 'Rajkot',
      'kalyan_dombivali': 'Kalyan-Dombivali',
      'vasai_virar': 'Vasai-Virar',
      'varanasi': 'Varanasi',
      'srinagar': 'Srinagar',
      'aurangabad': 'Aurangabad',
      'dhanbad': 'Dhanbad',
      'amritsar': 'Amritsar',
      'navi_mumbai': 'Navi Mumbai',
      'allahabad': 'Allahabad',
      'ranchi': 'Ranchi',
      'howrah': 'Howrah',
      'coimbatore': 'Coimbatore',
      'jabalpur': 'Jabalpur',
      'gwalior': 'Gwalior',
      'vijayawada': 'Vijayawada',
      'jodhpur': 'Jodhpur',
      'madurai': 'Madurai',
      'raipur': 'Raipur',
      'kota': 'Kota',
      'gurgaon': 'Gurgaon',
      'chandigarh': 'Chandigarh',
      'solapur': 'Solapur',
      'hubli_dharwad': 'Hubli-Dharwad',
      'bareilly': 'Bareilly',
      'moradabad': 'Moradabad',
      'mysore': 'Mysore',
      'aligarh': 'Aligarh',
      'jalandhar': 'Jalandhar',
      'tiruchirappalli': 'Tiruchirappalli',
      'bhubaneswar': 'Bhubaneswar',
      'salem': 'Salem',
      'mira_bhayandar': 'Mira-Bhayandar',
      'warangal': 'Warangal',
      'thiruvananthapuram': 'Thiruvananthapuram',
      'guntur': 'Guntur',
      'bhiwandi': 'Bhiwandi',
      'saharanpur': 'Saharanpur',
      'gorakhpur': 'Gorakhpur',
      'bikaner': 'Bikaner',
      'amravati': 'Amravati',
      'noida': 'Noida',
      'jamshedpur': 'Jamshedpur',
      'bhilai': 'Bhilai',
      'cuttack': 'Cuttack',
      'firozabad': 'Firozabad',
      'kochi': 'Kochi',
      'nellore': 'Nellore',
      'bhavnagar': 'Bhavnagar',
      'dehradun': 'Dehradun',
      'durgapur': 'Durgapur',
      'asansol': 'Asansol',
      'rourkela': 'Rourkela',
      'nanded': 'Nanded',
      'kolhapur': 'Kolhapur',
      'ajmer': 'Ajmer',
      'akola': 'Akola',
      'gulbarga': 'Gulbarga',
      'jamnagar': 'Jamnagar',
      'ujjain': 'Ujjain',
      'loni': 'Loni',
      'siliguri': 'Siliguri',
      'jhansi': 'Jhansi',
      'ulhasnagar': 'Ulhasnagar',
      'jammu': 'Jammu',
      'sangli_miraj_kupwad': 'Sangli-Miraj-Kupwad',
      'mangalore': 'Mangalore',
      'erode': 'Erode',
      'belgaum': 'Belgaum',
      'ambattur': 'Ambattur',
      'tirunelveli': 'Tirunelveli',
      'malegaon': 'Malegaon',
      'gaya': 'Gaya',
      'jalgaon': 'Jalgaon',
      'udaipur': 'Udaipur',
      'maheshtala': 'Maheshtala',
    };
    return cityMap[cityId.toLowerCase()];
  }

  /// Pre-populate state mappings for common countries
  void _prePopulateStateMappings() {
    // Add common Indian states
    const indianStates = {
      'India_MH': 'Maharashtra',
      'India_DL': 'Delhi',
      'India_KA': 'Karnataka',
      'India_TN': 'Tamil Nadu',
      'India_UP': 'Uttar Pradesh',
      'India_WB': 'West Bengal',
      'India_RJ': 'Rajasthan',
      'India_GJ': 'Gujarat',
      'India_MP': 'Madhya Pradesh',
      'India_AP': 'Andhra Pradesh',
      'India_TS': 'Telangana',
      'India_BR': 'Bihar',
      'India_OR': 'Odisha',
      'India_AS': 'Assam',
      'India_PB': 'Punjab',
      'India_HR': 'Haryana',
      'India_JH': 'Jharkhand',
      'India_CT': 'Chhattisgarh',
      'India_UK': 'Uttarakhand',
      'India_HP': 'Himachal Pradesh',
      'India_JK': 'Jammu and Kashmir',
      'India_KL': 'Kerala',
      'India_GA': 'Goa',
      'India_MN': 'Manipur',
      'India_ML': 'Meghalaya',
      'India_MZ': 'Mizoram',
      'India_NL': 'Nagaland',
      'India_SK': 'Sikkim',
      'India_TR': 'Tripura',
      'India_AR': 'Arunachal Pradesh',
    };

    _stateCache.addAll(indianStates);

    // Add common US states
    const usStates = {
      'United States_CA': 'California',
      'United States_TX': 'Texas',
      'United States_FL': 'Florida',
      'United States_NY': 'New York',
      'United States_PA': 'Pennsylvania',
      'United States_IL': 'Illinois',
      'United States_OH': 'Ohio',
      'United States_GA': 'Georgia',
      'United States_NC': 'North Carolina',
      'United States_MI': 'Michigan',
    };

    _stateCache.addAll(usStates);
  }

  /// Clear all caches (useful for testing or memory management)
  void clearCache() {
    _countryCache.clear();
    _stateCache.clear();
    _cityCache.clear();
    _isInitialized = false;
  }
}

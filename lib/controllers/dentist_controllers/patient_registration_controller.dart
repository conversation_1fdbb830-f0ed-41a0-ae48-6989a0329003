import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/data/models/patient_registration_model.dart';
import 'package:platix/services/api_service.dart';
import '../../api/config.dart';
import '../../utils/app_utils.dart';

class PatientRegistrationController extends GetxController {
  bool isLoading = false;
  List<PatientRegistration> patientRegistrations = [];
  Pagination? pagination;

  // Location service for ID-to-name conversion
  
  // Search and pagination parameters
  final TextEditingController searchController = TextEditingController();
  int currentPage = 1;
  int limit = 10;
  String searchQuery = '';
  
  // For handling refresh and load more
  bool isLoadingMore = false;
  bool hasMoreData = true;
  bool isResolvingLocations = false;

  @override
  void onInit() {
    super.onInit();
    _initializeLocationService();
    getPatientRegistrations();
  }

  /// Initialize the location service
  Future<void> _initializeLocationService() async {
    try {
      await _locationService.initialize();
      log('✅ LocationService initialized successfully');

      // Test the service with sample data
      String testCountry = _locationService.getCountryName('IN');
      String testState = await _locationService.getStateName('MH', 'India');
      String testCity = await _locationService.getCityName('mumbai', 'Maharashtra', 'India');
      log('🧪 LocationService test - Country: $testCountry, State: $testState, City: $testCity');
    } catch (e) {
      log('❌ LocationService initialization failed: $e');
    }
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  /// Get patient registrations with pagination and search
  Future<void> getPatientRegistrations({
    int page = 1,
    int pageLimit = 10,
    String search = '',
    bool isRefresh = false,
    bool isLoadMore = false,
  }) async {
    try {
      if (isRefresh) {
        currentPage = 1;
        hasMoreData = true;
        patientRegistrations.clear();
      }

      if (isLoadMore) {
        if (!hasMoreData || isLoadingMore) return;
        isLoadingMore = true;
        currentPage++;
      } else {
        isLoading = true;
      }

      update();

      // Build query parameters
      String queryParams = '?page=${isLoadMore ? currentPage : page}&limit=$pageLimit';
      if (search.isNotEmpty) {
        queryParams += '&search=${Uri.encodeComponent(search)}';
      }

      String endpoint = '${Config.baseUrl}${Config.patientRegistrationList}$queryParams';
      
      log('🌍 Requesting Patient Registrations: $endpoint');

      var response = await ApiService.getRequest(endpoint, addBaseUrl: false);

      log('📥 API Response: ${jsonEncode(response)}');

      if (response.containsKey('data')) {
        PatientRegistrationResponse registrationResponse =
            PatientRegistrationResponse.fromJson(response);

        // Resolve location names for the received data
        isResolvingLocations = true;
        update();

        List<PatientRegistration> resolvedData;
        try {
          resolvedData = await _locationService
              .resolveLocationNamesForList(registrationResponse.data);
          log('✅ Location names resolved for ${resolvedData.length} patients');
        } catch (e) {
          log('❌ Location resolution failed: $e');
          // Use original data if resolution fails
          resolvedData = registrationResponse.data;
        }

        isResolvingLocations = false;

        if (isLoadMore) {
          // Append new data for load more
          patientRegistrations.addAll(resolvedData);
        } else {
          // Replace data for new search or refresh
          patientRegistrations = resolvedData;
        }

        pagination = registrationResponse.pagination;
        
        // Update pagination state
        if (!isLoadMore) {
          currentPage = registrationResponse.pagination.page;
        }
        
        // Check if there's more data
        hasMoreData = currentPage < registrationResponse.pagination.totalPages;
        
        log('✅ Patient Registrations Loaded Successfully');
        log('📊 Total: ${registrationResponse.pagination.total}, Page: ${registrationResponse.pagination.page}/${registrationResponse.pagination.totalPages}');
      } else {
        log('❌ API response missing expected data');
        AppUtils.showToastMessage('Failed to Load Patient Registrations');
      }
    } catch (e) {
      log('❌ Exception: $e');
      AppUtils.showToastMessage('Something went wrong!');
    } finally {
      isLoading = false;
      isLoadingMore = false;
      update();
    }
  }

  /// Search patient registrations
  Future<void> searchPatientRegistrations(String query) async {
    searchQuery = query;
    await getPatientRegistrations(
      page: 1,
      pageLimit: limit,
      search: query,
      isRefresh: true,
    );
  }

  /// Refresh patient registrations
  Future<void> refreshPatientRegistrations() async {
    await getPatientRegistrations(
      page: 1,
      pageLimit: limit,
      search: searchQuery,
      isRefresh: true,
    );
  }

  /// Load more patient registrations
  Future<void> loadMorePatientRegistrations() async {
    await getPatientRegistrations(
      pageLimit: limit,
      search: searchQuery,
      isLoadMore: true,
    );
  }

  /// Clear search
  void clearSearch() {
    searchController.clear();
    searchQuery = '';
    getPatientRegistrations(isRefresh: true);
  }

  /// Delete patient registration (placeholder for future implementation)
  Future<void> deletePatientRegistration(String id) async {
    try {
      // TODO: Implement delete API call when backend provides the endpoint
      log('🗑️ Delete patient registration: $id');
      
      // For now, just remove from local list
      patientRegistrations.removeWhere((patient) => patient.id == id);
      update();
      
      AppUtils.showToastMessage('Patient registration deleted successfully');
    } catch (e) {
      log('❌ Delete Exception: $e');
      AppUtils.showToastMessage('Failed to delete patient registration');
    }
  }

  /// Get patient registration by ID (for edit functionality)
  PatientRegistration? getPatientById(String id) {
    try {
      return patientRegistrations.firstWhere((patient) => patient.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Resolve location names for a single patient (useful for edit mode)
  Future<PatientRegistration?> getPatientByIdWithResolvedLocation(String id) async {
    try {
      PatientRegistration? patient = getPatientById(id);
      if (patient != null) {
        return await _locationService.resolveLocationNames(patient);
      }
      return null;
    } catch (e) {
      log('❌ Failed to resolve location for patient $id: $e');
      return getPatientById(id); // Return original patient if resolution fails
    }
  }
}
